<template>
  <div class="app">
    <!-- <div class="head">
          <div class="titles">话题管理</div>
          <div class="title1">Pages/宝妈社区/话题管理</div>
      </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2"></h2>
        <div class="contentHeads">
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">联系电话</p>
              <el-input
                v-model="ruleForm.tel"
                placeholder="请输入联系电话"
                style="width: 260px"
                clearable
              ></el-input>
            </div>
          </div>
          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="inquire"
            >查询</el-button
          >
          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="reset"
            >重置</el-button
          >
        </div>
        <el-table v-loading="loading" :data="listData" class="table">
          <el-table-column label="序号" align="center">
            <template scope="scope">
              <span v-text="scope.$index + 1"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="用户名"
            prop="nickname"
            :show-overflow-tooltip="true"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="手机号"
            prop="tel"
            :show-overflow-tooltip="true"
            align="center"
          >
          </el-table-column>
          <el-table-column label="反馈内容" prop="content" align="center" />
          <el-table-column label="创建时间" prop="createTime" align="center" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此请帖反馈？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { page, delTable } from "@/api/platform/invitationfeedback";
export default {
  name: "app",
  data() {
    return {
      currentPage: 1,
      total: 0,
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
        tel: "",
      },
      deleteDialogVisible: false,
      listData: [],
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    reset() {
      this.currentPage = 1;
      this.ruleForm = {
        pageSize: 10,
        pageNum: 1,
        tel: "",
      };
      this.getList();
    },
    inquire() {
      //查询
      this.ruleForm.pageNum = 1;
      this.currentPage = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleDelete(row) {
      this.diaryId = row.id;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      //确定删除
      delTable(this.diaryId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.TemplateList {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 每行三个等宽列 */
  gap: 10px; /* 行与列之间的间隔 */
}
.activeTemplate {
  padding: 20px 20px;
  background: #e8eeff;
  color: #3f6fff;
  font-size: 10px;
}
.template {
  padding: 20px 20px;
  background: #f9f9f9;
  color: #747272;
  font-size: 10px;
}
.tagList {
  display: flex;
  align-items: center;
  margin-top: 10px;
  width: 100%;
  overflow-x: auto;
  white-space: nowrap; /* 防止内部元素换行 */
}
.activeTag {
  padding: 4px 20px;
  background: #3f85ff;
  color: #ffffff;
  font-size: 12px;
  border-radius: 4px;
  margin-right: 20px;
  margin-bottom: 10px;
}
.tag {
  padding: 4px 20px;
  color: #747272;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #dcdcdc;
  margin-right: 20px;
  margin-bottom: 10px;
}
.fgx {
  width: 100%;
  height: 10px;
  background: #f0f1f5;
}
.addList {
  display: flex;
}
.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  margin: 20px 20px;
  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2 {
      color: #17191a;
    }
  }
}
.contentHeads {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.roomMessage {
  display: flex;
}
.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;
  .selTitle {
    width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.block {
  text-align: right;
  margin-top: 20px;
}
</style>
